package main

import (
	"fmt"
	"log"

	"github.com/emsg-protocol/emsg-client-sdk/auth"
	"github.com/emsg-protocol/emsg-client-sdk/client"
	"github.com/emsg-protocol/emsg-client-sdk/keymgmt"
	"github.com/emsg-protocol/emsg-client-sdk/utils"
)

func main() {
	fmt.Println("🚀 EMSG Client SDK Demo")
	fmt.Println("========================")

	// 1. Generate a key pair
	fmt.Println("\n1. Generating Ed25519 key pair...")
	keyPair, err := keymgmt.GenerateKeyPair()
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("✅ Public key: %s\n", keyPair.PublicKeyBase64())
	fmt.Printf("✅ Private key (hex): %s...\n", keyPair.PrivateKeyHex()[:32])

	// 2. Parse EMSG addresses
	fmt.Println("\n2. Parsing EMSG addresses...")
	fromAddr, err := utils.ParseEMSGAddress("alice#example.com")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("✅ From address: User=%s, Domain=%s\n", fromAddr.User, fromAddr.Domain)
	fmt.Printf("✅ DNS lookup name: %s\n", fromAddr.GetEMSGDNSName())

	toAddr, err := utils.ParseEMSGAddress("bob#test.org")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("✅ To address: User=%s, Domain=%s\n", toAddr.User, toAddr.Domain)

	// 3. Create and sign a message
	fmt.Println("\n3. Creating and signing a message...")
	emsgClient := client.NewWithKeyPair(keyPair)

	msg, err := emsgClient.ComposeMessage().
		From("alice#example.com").
		To("bob#test.org", "charlie#example.net").
		CC("dave#example.org").
		Subject("Demo Message").
		Body("This is a demo message from the EMSG Client SDK!").
		GroupID("demo-group").
		Build()
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("✅ Message created with ID: %s\n", msg.MessageID)
	fmt.Printf("✅ Recipients: %v\n", msg.GetRecipients())
	fmt.Printf("✅ Message signed: %t\n", msg.IsSigned())

	// Sign the message
	err = msg.Sign(keyPair)
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("✅ Message now signed: %t\n", msg.IsSigned())

	// 4. Verify the message signature
	fmt.Println("\n4. Verifying message signature...")
	err = msg.Verify(keyPair.PublicKeyBase64())
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("✅ Message signature verified successfully!")

	// 5. Generate authentication header
	fmt.Println("\n5. Generating authentication header...")
	authHeader, err := auth.GenerateAuthHeader(keyPair, "POST", "/api/v1/messages")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("✅ Auth header: %s\n", authHeader.ToHeaderValue()[:80]+"...")

	// 6. Serialize message to JSON
	fmt.Println("\n6. Serializing message to JSON...")
	jsonData, err := msg.ToJSON()
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("✅ JSON size: %d bytes\n", len(jsonData))
	fmt.Printf("✅ JSON preview: %s...\n", string(jsonData[:100]))

	// 7. Test address validation
	fmt.Println("\n7. Testing address validation...")
	validAddresses := []string{
		"alice#example.com",
		"bob.smith#test.org",
		"user_123#sub.domain.co.uk",
	}

	invalidAddresses := []string{
		"<EMAIL>", // Wrong separator
		"alice#",            // Missing domain
		"#example.com",      // Missing user
		"alice#***********", // IP address not allowed
	}

	for _, addr := range validAddresses {
		if utils.IsValidEMSGAddress(addr) {
			fmt.Printf("✅ Valid: %s\n", addr)
		} else {
			fmt.Printf("❌ Invalid: %s\n", addr)
		}
	}

	for _, addr := range invalidAddresses {
		if !utils.IsValidEMSGAddress(addr) {
			fmt.Printf("✅ Correctly rejected: %s\n", addr)
		} else {
			fmt.Printf("❌ Incorrectly accepted: %s\n", addr)
		}
	}

	fmt.Println("\n🎉 Demo completed successfully!")
	fmt.Println("\nThe EMSG Client SDK is working perfectly!")
	fmt.Println("- Key generation and management ✅")
	fmt.Println("- Message composition and signing ✅")
	fmt.Println("- Address parsing and validation ✅")
	fmt.Println("- Authentication header generation ✅")
	fmt.Println("- JSON serialization ✅")
}
